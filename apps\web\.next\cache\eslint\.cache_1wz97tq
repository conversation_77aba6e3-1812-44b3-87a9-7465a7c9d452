[{"G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\firecrawl\\scrape\\route.ts": "1", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\runs\\feedback\\route.ts": "2", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\runs\\share\\route.ts": "3", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\delete\\id\\route.ts": "4", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\delete\\route.ts": "5", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\get\\route.ts": "6", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\put\\route.ts": "7", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\whisper\\audio\\route.ts": "8", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\[..._path]\\route.ts": "9", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\callback\\route.ts": "10", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\confirm\\route.ts": "11", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\login\\page.tsx": "12", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signout\\page.tsx": "13", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signup\\page.tsx": "14", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signup\\success\\page.tsx": "15", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\layout.tsx": "16", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\page.tsx": "17", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\code\\index.tsx": "18", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\code\\PortToLanguage.tsx": "19", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\FullPrompt.tsx": "20", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\index.tsx": "21", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\NewCustomQuickActionDialog.tsx": "22", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\index.tsx": "23", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\index.tsx": "24", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\LengthOptions.tsx": "25", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\ReadingLevelOptions.tsx": "26", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\TranslateOptions.tsx": "27", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\ArtifactLoading.tsx": "28", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\ArtifactRenderer.tsx": "29", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\CodeRenderer.tsx": "30", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\components\\AskOpenCanvas.tsx": "31", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\components\\CopyText.tsx": "32", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\artifact-title.tsx": "33", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\index.tsx": "34", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\navigate-artifact-history.tsx": "35", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\TextRenderer.tsx": "36", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\assistant-item.tsx": "37", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\color-picker.tsx": "38", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\context-documents\\index.tsx": "39", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\context-documents\\uploaded-file.tsx": "40", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\create-edit-assistant-dialog.tsx": "41", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\edit-delete-dropdown.tsx": "42", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\icon-select.tsx": "43", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\index.tsx": "44", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\utils.tsx": "45", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-ui\\attachment.tsx": "46", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-ui\\tooltip-icon-button.tsx": "47", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\actions.ts": "48", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\Login.tsx": "49", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\user-auth-form-login.tsx": "50", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\actions.ts": "51", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\Signup.tsx": "52", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\success\\index.tsx": "53", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\user-auth-form-signup.tsx": "54", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\canavas-loading.tsx": "55", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\canvas.tsx": "56", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\content-composer.tsx": "57", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\index.ts": "58", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\composer-actions-popout.tsx": "59", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\composer.tsx": "60", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\drag-drop-wrapper.tsx": "61", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\feedback.tsx": "62", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\index.tsx": "63", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\messages.tsx": "64", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\index.tsx": "65", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\model-config-pannel.tsx": "66", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\new-badge.tsx": "67", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\thread-history.tsx": "68", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\thread.tsx": "69", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\welcome.tsx": "70", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\flags.tsx": "71", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\langsmith.tsx": "72", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\magic_pencil.tsx": "73", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\NoSSRWrapper.tsx": "74", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\reflections-dialog\\ConfirmClearDialog.tsx": "75", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\reflections-dialog\\ReflectionsDialog.tsx": "76", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\tool-hooks\\AttachmentsToolUI.tsx": "77", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\tool-hooks\\LangSmithLinkToolUI.tsx": "78", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\accordion.tsx": "79", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\alert.tsx": "80", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\audio.ts": "81", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\pdf.ts": "82", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\video.ts": "83", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-ui.tsx": "84", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\avatar.tsx": "85", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\markdown-text.tsx": "86", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\syntax-highlighter.tsx": "87", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\tooltip-icon-button.tsx": "88", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\utils\\withDefaults.tsx": "89", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\avatar.tsx": "90", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\badge.tsx": "91", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\button.tsx": "92", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\card.tsx": "93", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\checkbox.tsx": "94", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\command.tsx": "95", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\dialog.tsx": "96", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\dropdown-menu.tsx": "97", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\header.tsx": "98", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\hover-card.tsx": "99", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\icons.tsx": "100", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\inline-context-tooltip.tsx": "101", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\input.tsx": "102", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\label.tsx": "103", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\language-switcher.tsx": "104", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\password-input.tsx": "105", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\popover.tsx": "106", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\programming-lang-dropdown.tsx": "107", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\progress.tsx": "108", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\resizable.tsx": "109", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\select.tsx": "110", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\sheet.tsx": "111", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\skeleton.tsx": "112", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\slider.tsx": "113", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\textarea.tsx": "114", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\toast.tsx": "115", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\toaster.tsx": "116", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\tooltip.tsx": "117", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\web-search-results\\index.tsx": "118", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\web-search-results\\loading-cards.tsx": "119", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\constants.ts": "120", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\AssistantContext.tsx": "121", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\GraphContext.tsx": "122", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\LanguageContext.tsx": "123", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\ThreadProvider.tsx": "124", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\UserContext.tsx": "125", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\utils.ts": "126", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\use-toast.ts": "127", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useContextDocuments.tsx": "128", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useFeedback.ts": "129", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useLocalStorage.tsx": "130", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useRuns.tsx": "131", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useStore.tsx": "132", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\utils.ts": "133", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\attachments.tsx": "134", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\convert_messages.ts": "135", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\cookies.ts": "136", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\get_language_template.ts": "137", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\normalize_string.ts": "138", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\store.ts": "139", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\client.ts": "140", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\middleware.ts": "141", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\server.ts": "142", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\verify_user_server.ts": "143", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\utils.ts": "144", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\middleware.ts": "145", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\types.ts": "146", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\stream.worker.ts": "147", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\streamWorker.ts": "148", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\streamWorker.types.ts": "149", "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\test.js": "150"}, {"size": 1655, "mtime": 1755481666406, "results": "151", "hashOfConfig": "152"}, {"size": 2130, "mtime": 1755481666406, "results": "153", "hashOfConfig": "152"}, {"size": 1736, "mtime": 1755481666406, "results": "154", "hashOfConfig": "152"}, {"size": 1687, "mtime": 1755481666406, "results": "155", "hashOfConfig": "152"}, {"size": 1205, "mtime": 1755481666406, "results": "156", "hashOfConfig": "152"}, {"size": 1206, "mtime": 1755481666406, "results": "157", "hashOfConfig": "152"}, {"size": 1216, "mtime": 1755481666406, "results": "158", "hashOfConfig": "152"}, {"size": 2223, "mtime": 1755481666406, "results": "159", "hashOfConfig": "152"}, {"size": 3659, "mtime": 1755492333846, "results": "160", "hashOfConfig": "152"}, {"size": 1242, "mtime": 1755481666406, "results": "161", "hashOfConfig": "152"}, {"size": 1227, "mtime": 1755481666406, "results": "162", "hashOfConfig": "152"}, {"size": 286, "mtime": 1755481666406, "results": "163", "hashOfConfig": "152"}, {"size": 879, "mtime": 1755481666406, "results": "164", "hashOfConfig": "152"}, {"size": 290, "mtime": 1755481666406, "results": "165", "hashOfConfig": "152"}, {"size": 251, "mtime": 1755481666406, "results": "166", "hashOfConfig": "152"}, {"size": 646, "mtime": 1755481666406, "results": "167", "hashOfConfig": "152"}, {"size": 784, "mtime": 1755498397213, "results": "168", "hashOfConfig": "152"}, {"size": 5196, "mtime": 1755503011648, "results": "169", "hashOfConfig": "152"}, {"size": 1522, "mtime": 1755481666406, "results": "170", "hashOfConfig": "152"}, {"size": 4855, "mtime": 1755481666406, "results": "171", "hashOfConfig": "152"}, {"size": 8008, "mtime": 1755481666406, "results": "172", "hashOfConfig": "152"}, {"size": 11890, "mtime": 1755481666406, "results": "173", "hashOfConfig": "152"}, {"size": 48, "mtime": 1755481666406, "results": "174", "hashOfConfig": "152"}, {"size": 5121, "mtime": 1755481666406, "results": "175", "hashOfConfig": "152"}, {"size": 2488, "mtime": 1755481666406, "results": "176", "hashOfConfig": "152"}, {"size": 2181, "mtime": 1755481666406, "results": "177", "hashOfConfig": "152"}, {"size": 2151, "mtime": 1755481666406, "results": "178", "hashOfConfig": "152"}, {"size": 1287, "mtime": 1755481666406, "results": "179", "hashOfConfig": "152"}, {"size": 13940, "mtime": 1755481666406, "results": "180", "hashOfConfig": "152"}, {"size": 3737, "mtime": 1755481666406, "results": "181", "hashOfConfig": "152"}, {"size": 4052, "mtime": 1755481666406, "results": "182", "hashOfConfig": "152"}, {"size": 1723, "mtime": 1755481666406, "results": "183", "hashOfConfig": "152"}, {"size": 1318, "mtime": 1755481666406, "results": "184", "hashOfConfig": "152"}, {"size": 2184, "mtime": 1755503011648, "results": "185", "hashOfConfig": "152"}, {"size": 1786, "mtime": 1755481666406, "results": "186", "hashOfConfig": "152"}, {"size": 9108, "mtime": 1755670438614, "results": "187", "hashOfConfig": "152"}, {"size": 2387, "mtime": 1755481666406, "results": "188", "hashOfConfig": "152"}, {"size": 2308, "mtime": 1755481666406, "results": "189", "hashOfConfig": "152"}, {"size": 6861, "mtime": 1755481666406, "results": "190", "hashOfConfig": "152"}, {"size": 2598, "mtime": 1755481666406, "results": "191", "hashOfConfig": "152"}, {"size": 12925, "mtime": 1755481666406, "results": "192", "hashOfConfig": "152"}, {"size": 3094, "mtime": 1755481666406, "results": "193", "hashOfConfig": "152"}, {"size": 5895, "mtime": 1755481666406, "results": "194", "hashOfConfig": "152"}, {"size": 6109, "mtime": 1755481666406, "results": "195", "hashOfConfig": "152"}, {"size": 327, "mtime": 1755481666406, "results": "196", "hashOfConfig": "152"}, {"size": 5656, "mtime": 1755481666410, "results": "197", "hashOfConfig": "152"}, {"size": 1094, "mtime": 1755481666410, "results": "198", "hashOfConfig": "152"}, {"size": 583, "mtime": 1755481666410, "results": "199", "hashOfConfig": "152"}, {"size": 3355, "mtime": 1755481666410, "results": "200", "hashOfConfig": "152"}, {"size": 4494, "mtime": 1755481666410, "results": "201", "hashOfConfig": "152"}, {"size": 856, "mtime": 1755481666410, "results": "202", "hashOfConfig": "152"}, {"size": 3558, "mtime": 1755481666410, "results": "203", "hashOfConfig": "152"}, {"size": 1696, "mtime": 1755481666410, "results": "204", "hashOfConfig": "152"}, {"size": 4506, "mtime": 1755481666410, "results": "205", "hashOfConfig": "152"}, {"size": 2080, "mtime": 1755481666410, "results": "206", "hashOfConfig": "152"}, {"size": 8404, "mtime": 1755481666410, "results": "207", "hashOfConfig": "152"}, {"size": 5211, "mtime": 1755481666410, "results": "208", "hashOfConfig": "152"}, {"size": 61, "mtime": 1755481666410, "results": "209", "hashOfConfig": "152"}, {"size": 5012, "mtime": 1755503733279, "results": "210", "hashOfConfig": "152"}, {"size": 4050, "mtime": 1755503569332, "results": "211", "hashOfConfig": "152"}, {"size": 2867, "mtime": 1755481666410, "results": "212", "hashOfConfig": "152"}, {"size": 1946, "mtime": 1755481666410, "results": "213", "hashOfConfig": "152"}, {"size": 26, "mtime": 1755481666410, "results": "214", "hashOfConfig": "152"}, {"size": 6221, "mtime": 1755481666410, "results": "215", "hashOfConfig": "152"}, {"size": 12958, "mtime": 1755593903011, "results": "216", "hashOfConfig": "152"}, {"size": 4679, "mtime": 1755498580545, "results": "217", "hashOfConfig": "152"}, {"size": 947, "mtime": 1755481666410, "results": "218", "hashOfConfig": "152"}, {"size": 7876, "mtime": 1755503640071, "results": "219", "hashOfConfig": "152"}, {"size": 6483, "mtime": 1755503011688, "results": "220", "hashOfConfig": "152"}, {"size": 6951, "mtime": 1755509307367, "results": "221", "hashOfConfig": "152"}, {"size": 18400, "mtime": 1755481666410, "results": "222", "hashOfConfig": "152"}, {"size": 8059, "mtime": 1755481666410, "results": "223", "hashOfConfig": "152"}, {"size": 1430, "mtime": 1755481666410, "results": "224", "hashOfConfig": "152"}, {"size": 269, "mtime": 1755481666406, "results": "225", "hashOfConfig": "152"}, {"size": 1623, "mtime": 1755481666410, "results": "226", "hashOfConfig": "152"}, {"size": 7703, "mtime": 1755481666410, "results": "227", "hashOfConfig": "152"}, {"size": 537, "mtime": 1755481666410, "results": "228", "hashOfConfig": "152"}, {"size": 952, "mtime": 1755481666410, "results": "229", "hashOfConfig": "152"}, {"size": 2044, "mtime": 1755481666410, "results": "230", "hashOfConfig": "152"}, {"size": 1609, "mtime": 1755481666410, "results": "231", "hashOfConfig": "152"}, {"size": 810, "mtime": 1755481666410, "results": "232", "hashOfConfig": "152"}, {"size": 752, "mtime": 1755481666410, "results": "233", "hashOfConfig": "152"}, {"size": 765, "mtime": 1755481666410, "results": "234", "hashOfConfig": "152"}, {"size": 5244, "mtime": 1755481666410, "results": "235", "hashOfConfig": "152"}, {"size": 1051, "mtime": 1755481666410, "results": "236", "hashOfConfig": "152"}, {"size": 6766, "mtime": 1755481666410, "results": "237", "hashOfConfig": "152"}, {"size": 878, "mtime": 1755481666410, "results": "238", "hashOfConfig": "152"}, {"size": 1276, "mtime": 1755481666410, "results": "239", "hashOfConfig": "152"}, {"size": 1132, "mtime": 1755481666410, "results": "240", "hashOfConfig": "152"}, {"size": 1430, "mtime": 1755481666410, "results": "241", "hashOfConfig": "152"}, {"size": 1060, "mtime": 1755481666410, "results": "242", "hashOfConfig": "152"}, {"size": 1847, "mtime": 1755481666410, "results": "243", "hashOfConfig": "152"}, {"size": 1856, "mtime": 1755481666410, "results": "244", "hashOfConfig": "152"}, {"size": 1051, "mtime": 1755481666410, "results": "245", "hashOfConfig": "152"}, {"size": 5009, "mtime": 1755481666410, "results": "246", "hashOfConfig": "152"}, {"size": 4002, "mtime": 1755481666410, "results": "247", "hashOfConfig": "152"}, {"size": 7438, "mtime": 1755481666410, "results": "248", "hashOfConfig": "152"}, {"size": 227, "mtime": 1755481666410, "results": "249", "hashOfConfig": "152"}, {"size": 1207, "mtime": 1755481666410, "results": "250", "hashOfConfig": "152"}, {"size": 13165, "mtime": 1755481666410, "results": "251", "hashOfConfig": "152"}, {"size": 779, "mtime": 1755481666410, "results": "252", "hashOfConfig": "152"}, {"size": 828, "mtime": 1755481666410, "results": "253", "hashOfConfig": "152"}, {"size": 733, "mtime": 1755481666410, "results": "254", "hashOfConfig": "152"}, {"size": 2861, "mtime": 1755518539768, "results": "255", "hashOfConfig": "152"}, {"size": 1643, "mtime": 1755481666410, "results": "256", "hashOfConfig": "152"}, {"size": 1316, "mtime": 1755481666410, "results": "257", "hashOfConfig": "152"}, {"size": 4408, "mtime": 1755498686694, "results": "258", "hashOfConfig": "152"}, {"size": 799, "mtime": 1755481666410, "results": "259", "hashOfConfig": "152"}, {"size": 1754, "mtime": 1755481666410, "results": "260", "hashOfConfig": "152"}, {"size": 5674, "mtime": 1755481666410, "results": "261", "hashOfConfig": "152"}, {"size": 4331, "mtime": 1755481666410, "results": "262", "hashOfConfig": "152"}, {"size": 269, "mtime": 1755481666410, "results": "263", "hashOfConfig": "152"}, {"size": 2161, "mtime": 1755481666410, "results": "264", "hashOfConfig": "152"}, {"size": 738, "mtime": 1755481666410, "results": "265", "hashOfConfig": "152"}, {"size": 4883, "mtime": 1755481666410, "results": "266", "hashOfConfig": "152"}, {"size": 792, "mtime": 1755481666410, "results": "267", "hashOfConfig": "152"}, {"size": 1156, "mtime": 1755481666410, "results": "268", "hashOfConfig": "152"}, {"size": 5512, "mtime": 1755481666410, "results": "269", "hashOfConfig": "152"}, {"size": 862, "mtime": 1755481666410, "results": "270", "hashOfConfig": "152"}, {"size": 979, "mtime": 1755481666410, "results": "271", "hashOfConfig": "152"}, {"size": 12348, "mtime": 1755481666410, "results": "272", "hashOfConfig": "152"}, {"size": 51033, "mtime": 1755590706476, "results": "273", "hashOfConfig": "152"}, {"size": 14248, "mtime": 1755503810043, "results": "274", "hashOfConfig": "152"}, {"size": 8973, "mtime": 1755481666410, "results": "275", "hashOfConfig": "152"}, {"size": 1389, "mtime": 1755481666410, "results": "276", "hashOfConfig": "152"}, {"size": 11791, "mtime": 1755481666410, "results": "277", "hashOfConfig": "152"}, {"size": 4019, "mtime": 1755481666410, "results": "278", "hashOfConfig": "152"}, {"size": 3945, "mtime": 1755481666410, "results": "279", "hashOfConfig": "152"}, {"size": 2354, "mtime": 1755481666410, "results": "280", "hashOfConfig": "152"}, {"size": 1643, "mtime": 1755481666410, "results": "281", "hashOfConfig": "152"}, {"size": 514, "mtime": 1755481666410, "results": "282", "hashOfConfig": "152"}, {"size": 7224, "mtime": 1755481666410, "results": "283", "hashOfConfig": "152"}, {"size": 212, "mtime": 1755481666410, "results": "284", "hashOfConfig": "152"}, {"size": 8863, "mtime": 1755481666410, "results": "285", "hashOfConfig": "152"}, {"size": 4579, "mtime": 1755481666410, "results": "286", "hashOfConfig": "152"}, {"size": 610, "mtime": 1755481666410, "results": "287", "hashOfConfig": "152"}, {"size": 981, "mtime": 1755481666410, "results": "288", "hashOfConfig": "152"}, {"size": 619, "mtime": 1755481666410, "results": "289", "hashOfConfig": "152"}, {"size": 160, "mtime": 1755481666410, "results": "290", "hashOfConfig": "152"}, {"size": 461, "mtime": 1755481666414, "results": "291", "hashOfConfig": "152"}, {"size": 2615, "mtime": 1755481666414, "results": "292", "hashOfConfig": "152"}, {"size": 1023, "mtime": 1755481666414, "results": "293", "hashOfConfig": "152"}, {"size": 464, "mtime": 1755481666414, "results": "294", "hashOfConfig": "152"}, {"size": 169, "mtime": 1755481666414, "results": "295", "hashOfConfig": "152"}, {"size": 606, "mtime": 1755481666414, "results": "296", "hashOfConfig": "152"}, {"size": 1698, "mtime": 1755481666414, "results": "297", "hashOfConfig": "152"}, {"size": 1127, "mtime": 1755481666414, "results": "298", "hashOfConfig": "152"}, {"size": 848, "mtime": 1755481666414, "results": "299", "hashOfConfig": "152"}, {"size": 428, "mtime": 1755481666414, "results": "300", "hashOfConfig": "152"}, {"size": 2707, "mtime": 1755657877871, "results": "301", "hashOfConfig": "152"}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mfu5yz", {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\firecrawl\\scrape\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\runs\\feedback\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\runs\\share\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\delete\\id\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\delete\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\get\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\store\\put\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\whisper\\audio\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\api\\[..._path]\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\callback\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\confirm\\route.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\login\\page.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signout\\page.tsx", ["752"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signup\\page.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\auth\\signup\\success\\page.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\layout.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\app\\page.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\code\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\code\\PortToLanguage.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\FullPrompt.tsx", ["753"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\index.tsx", ["754"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\custom\\NewCustomQuickActionDialog.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\LengthOptions.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\ReadingLevelOptions.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\actions_toolbar\\text\\TranslateOptions.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\ArtifactLoading.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\ArtifactRenderer.tsx", ["755", "756"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\CodeRenderer.tsx", ["757"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\components\\AskOpenCanvas.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\components\\CopyText.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\artifact-title.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\header\\navigate-artifact-history.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\artifacts\\TextRenderer.tsx", ["758", "759", "760", "761", "762"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\assistant-item.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\color-picker.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\context-documents\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\context-documents\\uploaded-file.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\create-edit-assistant-dialog.tsx", ["763"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\edit-delete-dropdown.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\icon-select.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-select\\utils.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-ui\\attachment.tsx", [], ["764"], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\assistant-ui\\tooltip-icon-button.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\actions.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\Login.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\login\\user-auth-form-login.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\actions.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\Signup.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\success\\index.tsx", ["765"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\auth\\signup\\user-auth-form-signup.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\canavas-loading.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\canvas.tsx", ["766"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\content-composer.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\canvas\\index.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\composer-actions-popout.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\composer.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\drag-drop-wrapper.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\feedback.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\messages.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\index.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\model-config-pannel.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\model-selector\\new-badge.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\thread-history.tsx", ["767"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\thread.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\chat-interface\\welcome.tsx", ["768"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\flags.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\langsmith.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\icons\\magic_pencil.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\NoSSRWrapper.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\reflections-dialog\\ConfirmClearDialog.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\reflections-dialog\\ReflectionsDialog.tsx", ["769"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\tool-hooks\\AttachmentsToolUI.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\tool-hooks\\LangSmithLinkToolUI.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\accordion.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\alert.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\audio.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\pdf.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-adapters\\video.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\attachment-ui.tsx", [], ["770"], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\avatar.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\markdown-text.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\syntax-highlighter.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\tooltip-icon-button.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\assistant-ui\\utils\\withDefaults.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\avatar.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\badge.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\button.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\card.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\checkbox.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\command.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\dialog.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\dropdown-menu.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\header.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\hover-card.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\icons.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\inline-context-tooltip.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\input.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\label.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\language-switcher.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\password-input.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\popover.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\programming-lang-dropdown.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\progress.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\resizable.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\select.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\sheet.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\skeleton.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\slider.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\textarea.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\toast.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\toaster.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\ui\\tooltip.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\web-search-results\\index.tsx", ["771"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\components\\web-search-results\\loading-cards.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\constants.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\AssistantContext.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\GraphContext.tsx", ["772", "773", "774"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\LanguageContext.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\ThreadProvider.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\UserContext.tsx", ["775"], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\contexts\\utils.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\use-toast.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useContextDocuments.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useFeedback.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useLocalStorage.tsx", [], ["776"], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useRuns.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\useStore.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\hooks\\utils.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\attachments.tsx", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\convert_messages.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\cookies.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\get_language_template.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\normalize_string.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\store.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\client.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\middleware.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\server.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\supabase\\verify_user_server.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\lib\\utils.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\middleware.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\types.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\stream.worker.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\streamWorker.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\graph-stream\\streamWorker.types.ts", [], [], "G:\\Study\\Python\\open-canvas\\apps\\web\\src\\workers\\test.js", [], [], {"ruleId": "777", "severity": 1, "message": "778", "line": 22, "column": 6, "nodeType": "779", "endLine": 22, "endColumn": 8, "suggestions": "780"}, {"ruleId": "777", "severity": 1, "message": "781", "line": 47, "column": 6, "nodeType": "779", "endLine": 47, "endColumn": 23, "suggestions": "782"}, {"ruleId": "777", "severity": 1, "message": "783", "line": 113, "column": 6, "nodeType": "779", "endLine": 113, "endColumn": 25, "suggestions": "784"}, {"ruleId": "777", "severity": 1, "message": "785", "line": 249, "column": 6, "nodeType": "779", "endLine": 249, "endColumn": 39, "suggestions": "786"}, {"ruleId": "777", "severity": 1, "message": "787", "line": 256, "column": 6, "nodeType": "779", "endLine": 256, "endColumn": 41, "suggestions": "788"}, {"ruleId": "777", "severity": 1, "message": "789", "line": 76, "column": 6, "nodeType": "779", "endLine": 76, "endColumn": 38, "suggestions": "790"}, {"ruleId": "777", "severity": 1, "message": "791", "line": 117, "column": 6, "nodeType": "779", "endLine": 117, "endColumn": 32, "suggestions": "792"}, {"ruleId": "777", "severity": 1, "message": "793", "line": 117, "column": 7, "nodeType": "794", "endLine": 117, "endColumn": 31}, {"ruleId": "777", "severity": 1, "message": "787", "line": 123, "column": 6, "nodeType": "779", "endLine": 123, "endColumn": 28, "suggestions": "795"}, {"ruleId": "777", "severity": 1, "message": "796", "line": 157, "column": 6, "nodeType": "779", "endLine": 157, "endColumn": 48, "suggestions": "797"}, {"ruleId": "777", "severity": 1, "message": "798", "line": 175, "column": 6, "nodeType": "779", "endLine": 175, "endColumn": 25, "suggestions": "799"}, {"ruleId": "777", "severity": 1, "message": "800", "line": 155, "column": 6, "nodeType": "779", "endLine": 155, "endColumn": 40, "suggestions": "801"}, {"ruleId": "802", "severity": 1, "message": "803", "line": 71, "column": 5, "nodeType": "804", "endLine": 83, "endColumn": 7, "suppressions": "805"}, {"ruleId": "777", "severity": 1, "message": "806", "line": 33, "column": 6, "nodeType": "779", "endLine": 33, "endColumn": 15, "suggestions": "807"}, {"ruleId": "777", "severity": 1, "message": "808", "line": 55, "column": 6, "nodeType": "779", "endLine": 55, "endColumn": 32, "suggestions": "809"}, {"ruleId": "777", "severity": 1, "message": "810", "line": 213, "column": 6, "nodeType": "779", "endLine": 213, "endColumn": 12, "suggestions": "811"}, {"ruleId": "777", "severity": 1, "message": "812", "line": 105, "column": 5, "nodeType": "779", "endLine": 105, "endColumn": 23, "suggestions": "813"}, {"ruleId": "777", "severity": 1, "message": "814", "line": 82, "column": 6, "nodeType": "779", "endLine": 82, "endColumn": 25, "suggestions": "815"}, {"ruleId": "802", "severity": 1, "message": "803", "line": 84, "column": 5, "nodeType": "804", "endLine": 96, "endColumn": 7, "suppressions": "816"}, {"ruleId": "777", "severity": 1, "message": "817", "line": 113, "column": 6, "nodeType": "779", "endLine": 113, "endColumn": 36, "suggestions": "818"}, {"ruleId": "777", "severity": 1, "message": "819", "line": 161, "column": 6, "nodeType": "779", "endLine": 161, "endColumn": 21, "suggestions": "820"}, {"ruleId": "777", "severity": 1, "message": "821", "line": 208, "column": 6, "nodeType": "779", "endLine": 208, "endColumn": 37, "suggestions": "822"}, {"ruleId": "777", "severity": 1, "message": "823", "line": 238, "column": 6, "nodeType": "779", "endLine": 238, "endColumn": 42, "suggestions": "824"}, {"ruleId": "777", "severity": 1, "message": "825", "line": 27, "column": 6, "nodeType": "779", "endLine": 27, "endColumn": 8, "suggestions": "826"}, {"ruleId": "777", "severity": 1, "message": "827", "line": 52, "column": 6, "nodeType": "779", "endLine": 52, "endColumn": 8, "suggestions": "828", "suppressions": "829"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'router'. Either include it or remove the dependency array.", "ArrayExpression", ["830"], "React Hook useEffect has a missing dependency: 'isInitialLoad'. Either include it or remove the dependency array.", ["831"], "React Hook useEffect has a missing dependency: 'getAndSetCustomQuickActions'. Either include it or remove the dependency array.", ["832"], "React Hook useEffect has a missing dependency: 'artifact'. Either include it or remove the dependency array.", ["833"], "React Hook useEffect has a missing dependency: 'setSelectedBlocks'. Either include it or remove the dependency array.", ["834"], "React Hook useEffect has a missing dependency: 'setUpdateRenderedArtifactRequired'. Either include it or remove the dependency array.", ["835"], "React Hook useEffect has missing dependencies: 'artifact', 'editor', and 'setSelectedBlocks'. Either include them or remove the dependency array.", ["836"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", ["837"], "React Hook useEffect has missing dependencies: 'editor', 'isStreaming', 'manuallyUpdatingArtifact', and 'setUpdateRenderedArtifactRequired'. Either include them or remove the dependency array.", ["838"], "React Hook useEffect has a missing dependency: 'rawMarkdown'. Either include it or remove the dependency array.", ["839"], "React Hook useEffect has missing dependencies: 'getContextDocuments', 'metadata?.description', 'metadata?.iconData?.iconColor', 'metadata?.iconData?.iconName', 'setDocuments', 'setLoadingDocuments', 'setProcessedContextDocuments', and 'setUrls'. Either include them or remove the dependency array.", ["840"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["841"], "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["842"], "React Hook useEffect has missing dependencies: 'router' and 'searchParams'. Either include them or remove the dependency array.", ["843"], "React Hook useEffect has missing dependencies: 'getUserThreads' and 'userThreads.length'. Either include them or remove the dependency array.", ["844"], "React Hook useMemo has a missing dependency: 'getTranslatedPrompts'. Either include it or remove the dependency array.", ["845"], "React Hook useEffect has missing dependencies: 'getReflections', 'reflections.assistantId', 'reflections?.content', and 'reflections?.styleRules'. Either include them or remove the dependency array.", ["846"], ["847"], "React Hook useEffect has missing dependencies: 'open', 'setOpen', and 'setWebSearchResultsId'. Either include them or remove the dependency array. If 'setOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["848"], "React Hook useEffect has a missing dependency: 'assistantsData'. Either include it or remove the dependency array.", ["849"], "React Hook useEffect has missing dependencies: 'debouncedAPIUpdate', 'isStreaming', 'messages.length', 'threadSwitched', and 'updateRenderedArtifactRequired'. Either include them or remove the dependency array.", ["850"], "React Hook useEffect has missing dependencies: 'switchSelectedThread' and 'threadData'. Either include them or remove the dependency array.", ["851"], "React Hook useEffect has missing dependencies: 'getUser' and 'user'. Either include them or remove the dependency array.", ["852"], "React Hook useEffect has a missing dependency: 'readValue'. Either include it or remove the dependency array.", ["853"], ["854"], {"desc": "855", "fix": "856"}, {"desc": "857", "fix": "858"}, {"desc": "859", "fix": "860"}, {"desc": "861", "fix": "862"}, {"desc": "863", "fix": "864"}, {"desc": "865", "fix": "866"}, {"desc": "867", "fix": "868"}, {"desc": "869", "fix": "870"}, {"desc": "871", "fix": "872"}, {"desc": "873", "fix": "874"}, {"desc": "875", "fix": "876"}, {"kind": "877", "justification": "878"}, {"desc": "879", "fix": "880"}, {"desc": "881", "fix": "882"}, {"desc": "883", "fix": "884"}, {"desc": "885", "fix": "886"}, {"desc": "887", "fix": "888"}, {"kind": "877", "justification": "878"}, {"desc": "889", "fix": "890"}, {"desc": "891", "fix": "892"}, {"desc": "893", "fix": "894"}, {"desc": "895", "fix": "896"}, {"desc": "879", "fix": "897"}, {"desc": "898", "fix": "899"}, {"kind": "877", "justification": "878"}, "Update the dependencies array to be: [router]", {"range": "900", "text": "901"}, "Update the dependencies array to be: [isInitialLoad, props.highlight]", {"range": "902", "text": "903"}, "Update the dependencies array to be: [assistantId, getAndSetCustomQuickActions, user]", {"range": "904", "text": "905"}, "Update the dependencies array to be: [artifact, isSelectionActive, selectionBox]", {"range": "906", "text": "907"}, "Update the dependencies array to be: [selectedBlocks, isSelectionActive, setSelectedBlocks]", {"range": "908", "text": "909"}, "Update the dependencies array to be: [setUpdateRenderedArtifactRequired, updateRenderedArtifactRequired]", {"range": "910", "text": "911"}, "Update the dependencies array to be: [artifact, editor, setSelectedBlocks]", {"range": "912", "text": "913"}, "Update the dependencies array to be: [props.isInputVisible, setSelectedBlocks]", {"range": "914", "text": "915"}, "Update the dependencies array to be: [artifact, editor, isStreaming, manuallyUpdatingArtifact, setUpdateRenderedArtifactRequired, updateRenderedArtifactRequired]", {"range": "916", "text": "917"}, "Update the dependencies array to be: [is<PERSON><PERSON><PERSON><PERSON><PERSON>, editor, rawMarkdown]", {"range": "918", "text": "919"}, "Update the dependencies array to be: [getContextDocuments, metadata?.description, metadata?.iconData?.iconColor, metadata?.iconData?.iconName, props.assistant, props.isEditing, setDocuments, setLoadingDocuments, setProcessedContextDocuments, setUrls]", {"range": "920", "text": "921"}, "directive", "", "Update the dependencies array to be: [getUser, user]", {"range": "922", "text": "923"}, "Update the dependencies array to be: [chatCollapsedSearchParam, router, searchParams]", {"range": "924", "text": "925"}, "Update the dependencies array to be: [getUserThreads, user, userThreads.length]", {"range": "926", "text": "927"}, "Update the dependencies array to be: [getTranslatedPrompts]", {"range": "928", "text": "929"}, "Update the dependencies array to be: [getReflections, reflections.assistantId, reflections?.content, reflections?.styleRules, selectedAssistant]", {"range": "930", "text": "931"}, "Update the dependencies array to be: [webSearchResultsId, messages, open, setOpen, setWebSearchResultsId]", {"range": "932", "text": "933"}, "Update the dependencies array to be: [assistantsData, userData.user]", {"range": "934", "text": "935"}, "Update the dependencies array to be: [artifact, debouncedAPIUpdate, isStreaming, messages.length, threadData.threadId, threadSwitched, updateRenderedArtifactRequired]", {"range": "936", "text": "937"}, "Update the dependencies array to be: [switchSelectedThread, threadData, threadData.threadId, userData.user]", {"range": "938", "text": "939"}, {"range": "940", "text": "923"}, "Update the dependencies array to be: [readValue]", {"range": "941", "text": "942"}, [578, 580], "[router]", [1479, 1496], "[isInitialLoad, props.highlight]", [3321, 3340], "[assistantId, getAndSetCustomQuickActions, user]", [8749, 8782], "[artifact, isSelectionActive, selectionBox]", [8981, 9016], "[selectedBlocks, isSelectionActive, setSelectedBlocks]", [2316, 2348], "[setUpdateRenderedArtifactRequired, updateRenderedArtifactRequired]", [3523, 3549], "[artifact, editor, setSelectedBlocks]", [3653, 3675], "[props.isInputVisible, setSelectedBlocks]", [4681, 4723], "[artifact, editor, isStreaming, manuallyUpdatingArtifact, setUpdateRenderedArtifactRequired, updateRenderedArtifactRequired]", [5275, 5294], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, editor, rawMarkdown]", [4921, 4955], "[getContextDocuments, metadata?.description, metadata?.iconData?.iconColor, metadata?.iconData?.iconName, props.assistant, props.isEditing, setDocuments, setLoadingDocuments, setProcessedContextDocuments, setUrls]", [840, 849], "[getUser, user]", [2021, 2047], "[chatCollapsedSearchParam, router, searchParams]", [6019, 6025], "[getUserThreads, user, userThreads.length]", [3963, 3981], "[getTranslatedPrompts]", [2505, 2524], "[getReflections, reflections.assistantId, reflections?.content, reflections?.styleRules, selectedAssistant]", [3643, 3673], "[webSearchResultsId, messages, open, setOpen, setWebSearchResultsId]", [5464, 5479], "[assistantsData, userData.user]", [6960, 6991], "[artifact, debouncedAPIUpdate, isStreaming, messages.length, threadData.threadId, threadSwitched, updateRenderedArtifactRequired]", [7738, 7774], "[switchSelectedThread, threadData, threadData.threadId, userData.user]", [657, 659], [1568, 1570], "[readValue]"]