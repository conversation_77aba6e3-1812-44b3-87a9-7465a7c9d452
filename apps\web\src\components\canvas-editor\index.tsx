import React from 'react';

// 在实际项目中，这些数据可以从外部文件或API获取
const fontFamilies = [
  'Microsoft YaHei', '华文宋体', '华文黑体', '华文仿宋', '华文楷体',
  '华文琥珀', '华文隶书', '华文新魏', '华文行楷', '华文中宋',
  '华文彩云', 'Arial', 'Segoe UI', 'Ink Free', 'Fantasy'
];

const fontSizes = [
  { name: '初号', size: 56 }, { name: '小初', size: 48 }, { name: '一号', size: 34 },
  { name: '小一', size: 32 }, { name: '二号', size: 29 }, { name: '小二', size: 24 },
  { name: '三号', size: 21 }, { name: '小三', size: 20 }, { name: '四号', size: 18 },
  { name: '小四', size: 16 }, { name: '五号', size: 14 }, { name: '小五', size: 12 },
  { name: '六号', size: 10 }, { name: '小六', size: 8 }, { name: '七号', size: 7 },
  { name: '八号', size: 6 }
];

const CanvasEditor = () => {
  // 在这里可以使用 useState, useEffect 等 React Hooks 来管理状态和处理事件
  // 例如: const [font, setFont] = useState('微软雅黑');

  return (
    <div id="app">
      <div className="menu" editor-component="menu">
        <div className="menu-item">
          <div className="menu-item__undo">
            <i />
          </div>
          <div className="menu-item__redo">
            <i />
          </div>
          <div className="menu-item__painter" title="格式刷(双击可连续使用)">
            <i />
          </div>
          <div className="menu-item__format" title="清除格式">
            <i />
          </div>
        </div>
        <div className="menu-divider" />
        <div className="menu-item">
          <div className="menu-item__font">
            <span className="select" title="字体">微软雅黑</span>
            <div className="options">
              <ul>
                {fontFamilies.map(family => (
                  <li key={family} data-family={family} style={{ fontFamily: family }}>
                    {family}
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="menu-item__size">
            <span className="select" title="字体">小四</span>
            <div className="options">
              <ul>
                {fontSizes.map(item => (
                  <li key={item.name} data-size={item.size}>{item.name}</li>
                ))}
              </ul>
            </div>
          </div>
          <div className="menu-item__size-add">
            <i />
          </div>
          <div className="menu-item__size-minus">
            <i />
          </div>
          <div className="menu-item__bold">
            <i />
          </div>
          <div className="menu-item__italic">
            <i />
          </div>
          <div className="menu-item__underline">
            <i />
            <span className="select" />
            <div className="options">
              <ul>
                <li data-decoration-style="solid"><i /></li>
                <li data-decoration-style="double"><i /></li>
                <li data-decoration-style="dashed"><i /></li>
                <li data-decoration-style="dotted"><i /></li>
                <li data-decoration-style="wavy"><i /></li>
              </ul>
            </div>
          </div>
          <div className="menu-item__strikeout" title="删除线(Ctrl+Shift+X)">
            <i />
          </div>
          <div className="menu-item__superscript">
            <i />
          </div>
          <div className="menu-item__subscript">
            <i />
          </div>
          <div className="menu-item__color" title="字体颜色">
            <i />
            <span />
            <input type="color" id="color" />
          </div>
          <div className="menu-item__highlight" title="高亮">
            <i />
            <span />
            <input type="color" id="highlight" />
          </div>
        </div>
        <div className="menu-divider" />
        <div className="menu-item">
          <div className="menu-item__title">
            <i />
            <span className="select" title="切换标题">正文</span>
            <div className="options">
              <ul>
                <li style={{ fontSize: '16px' }}>正文</li>
                <li data-level="first" style={{ fontSize: '26px' }}>标题1</li>
                <li data-level="second" style={{ fontSize: '24px' }}>标题2</li>
                <li data-level="third" style={{ fontSize: '22px' }}>标题3</li>
                <li data-level="fourth" style={{ fontSize: '20px' }}>标题4</li>
                <li data-level="fifth" style={{ fontSize: '18px' }}>标题5</li>
                <li data-level="sixth" style={{ fontSize: '16px' }}>标题6</li>
              </ul>
            </div>
          </div>
          <div className="menu-item__left">
            <i />
          </div>
          <div className="menu-item__center">
            <i />
          </div>
          <div className="menu-item__right">
            <i />
          </div>
          <div className="menu-item__alignment">
            <i />
          </div>
          <div className="menu-item__justify">
            <i />
          </div>
          <div className="menu-item__row-margin">
            <i title="行间距" />
            <div className="options">
              <ul>
                <li data-rowmargin="1">1</li>
                <li data-rowmargin="1.25">1.25</li>
                <li data-rowmargin="1.5">1.5</li>
                <li data-rowmargin="1.75">1.75</li>
                <li data-rowmargin="2">2</li>
                <li data-rowmargin="2.5">2.5</li>
                <li data-rowmargin="3">3</li>
              </ul>
            </div>
          </div>
          <div className="menu-item__list">
            <i />
            <div className="options">
              <ul>
                <li>
                  <label>取消列表</label>
                </li>
                <li data-list-type="ol" data-list-style="decimal">
                  <label>有序列表：</label>
                  <ol>
                    <li>________</li>
                  </ol>
                </li>
                <li data-list-type="ul" data-list-style="checkbox">
                  <label>复选框列表：</label>
                  <ul style={{ listStyleType: "'☑️ '" }}>
                    <li>________</li>
                  </ul>
                </li>
                <li data-list-type="ul" data-list-style="disc">
                  <label>实心圆点列表：</label>
                  <ul style={{ listStyleType: 'disc' }}>
                    <li>________</li>
                  </ul>
                </li>
                <li data-list-type="ul" data-list-style="circle">
                  <label>空心圆点列表：</label>
                  <ul style={{ listStyleType: 'circle' }}>
                    <li>________</li>
                  </ul>
                </li>
                <li data-list-type="ul" data-list-style="square">
                  <label>空心方块列表：</label>
                  <ul style={{ listStyleType: "'☐ '" }}>
                    <li>________</li>
                  </ul>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="menu-divider" />
        <div className="menu-item">
          <div className="menu-item__table">
            <i title="表格" />
          </div>
          <div className="menu-item__table__collapse">
            <div className="table-close">×</div>
            <div className="table-title">
              <span className="table-select">插入</span>
              <span>表格</span>
            </div>
            <div className="table-panel" />
          </div>
          <div className="menu-item__image">
            <i title="图片" />
            <input type="file" id="image" accept=".png, .jpg, .jpeg, .svg, .gif" />
          </div>
          <div className="menu-item__hyperlink">
            <i title="超链接" />
          </div>
          <div className="menu-item__separator">
            <i title="分割线" />
            <div className="options">
              <ul>
                <li data-separator="0,0"><i /></li>
                <li data-separator="1,1"><i /></li>
                <li data-separator="3,1"><i /></li>
                <li data-separator="4,4"><i /></li>
                <li data-separator="7,3,3,3"><i /></li>
                <li data-separator="6,2,2,2,2,2"><i /></li>
              </ul>
            </div>
          </div>
          <div className="menu-item__watermark">
            <i title="水印(添加、删除)" />
            <div className="options">
              <ul>
                <li data-menu="add">添加水印</li>
                <li data-menu="delete">删除水印</li>
              </ul>
            </div>
          </div>
          <div className="menu-item__codeblock" title="代码块">
            <i />
          </div>
          <div className="menu-item__page-break" title="分页符">
            <i />
          </div>
          <div className="menu-item__control">
            <i title="控件" />
            <div className="options">
              <ul>
                <li data-control="text">文本</li>
                <li data-control="number">数值</li>
                <li data-control="select">列举</li>
                <li data-control="date">日期</li>
                <li data-control="checkbox">复选框</li>
                <li data-control="radio">单选框</li>
              </ul>
            </div>
          </div>
          <div className="menu-item__checkbox" title="复选框">
            <i />
          </div>
          <div className="menu-item__radio" title="单选框">
            <i />
          </div>
          <div className="menu-item__latex" title="LateX">
            <i />
          </div>
          <div className="menu-item__date">
            <i title="日期" />
            <div className="options">
              <ul>
                <li data-format="yyyy-MM-dd" />
                <li data-format="yyyy-MM-dd hh:mm:ss" />
              </ul>
            </div>
          </div>
          <div className="menu-item__block" title="内容块">
            <i />
          </div>
        </div>
        <div className="menu-divider" />
        <div className="menu-item">
          <div className="menu-item__search" data-menu="search">
            <i />
          </div>
          <div className="menu-item__search__collapse" data-menu="search">
            <div className="menu-item__search__collapse__search">
              <input type="text" />
              <label className="search-result" />
              <div className="arrow-left">
                <i />
              </div>
              <div className="arrow-right">
                <i />
              </div>
              <span>×</span>
            </div>
            <div className="menu-item__search__collapse__replace">
              <input type="text" />
              <button>替换</button>
            </div>
          </div>
          <div className="menu-item__print" data-menu="print">
            <i />
          </div>
        </div>
      </div>
      <div className="catalog" editor-component="catalog">
        <div className="catalog__header">
          <span>目录</span>
          <div className="catalog__header__close">
            <i />
          </div>
        </div>
        <div className="catalog__main" />
      </div>
      <div className="editor" />
      <div className="comment" editor-component="comment" />
      <div className="footer" editor-component="footer">
        <div>
          <div className="catalog-mode" title="目录">
            <i />
          </div>
          <div className="page-mode">
            <i title="页面模式(分页、连页)" />
            <div className="options">
              <ul>
                <li data-page-mode="paging" className="active">分页</li>
                <li data-page-mode="continuity">连页</li>
              </ul>
            </div>
          </div>
          <span>可见页码：<span className="page-no-list">1</span></span>
          <span>页面：<span className="page-no">1</span>/<span className="page-size">1</span></span>
          <span>字数：<span className="word-count">0</span></span>
          <span>行：<span className="row-no">0</span></span>
          <span>列：<span className="col-no">0</span></span>
        </div>
        <div className="editor-mode" title="编辑模式(编辑、清洁、只读、表单、设计)">编辑模式</div>
        <div>
          <div className="page-scale-minus" title="缩小(Ctrl+-)">
            <i />
          </div>
          <span className="page-scale-percentage" title="显示比例(点击可复原Ctrl+0)">100%</span>
          <div className="page-scale-add" title="放大(Ctrl+=)">
            <i />
          </div>
          <div className="paper-size">
            <i title="纸张类型" />
            <div className="options">
              <ul>
                <li data-paper-size="794*1123" className="active">A4</li>
                <li data-paper-size="1593*2251">A2</li>
                <li data-paper-size="1125*1593">A3</li>
                <li data-paper-size="565*796">A5</li>
                <li data-paper-size="412*488">5号信封</li>
                <li data-paper-size="450*866">6号信封</li>
                <li data-paper-size="609*862">7号信封</li>
                <li data-paper-size="862*1221">9号信封</li>
                <li data-paper-size="813*1266">法律用纸</li>
                <li data-paper-size="813*1054">信纸</li>
              </ul>
            </div>
          </div>
          <div className="paper-direction">
            <i title="纸张方向" />
            <div className="options">
              <ul>
                <li data-paper-direction="vertical" className="active">纵向</li>
                <li data-paper-direction="horizontal">横向</li>
              </ul>
            </div>
          </div>
          <div className="paper-margin" title="页边距">
            <i />
          </div>
          <div className="fullscreen" title="全屏显示">
            <i />
          </div>
          <div className="editor-option" title="编辑器设置">
            <i />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CanvasEditor;