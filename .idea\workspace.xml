<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0d69b7b1-a40d-4f31-8a6c-d7ab8735ea65" name="Changes" comment="支持glm模型">
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/yarn.lock" beforeDir="false" afterPath="$PROJECT_DIR$/yarn.lock" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="31UAIbVgyaQYM2GbF7rK1QrixoZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JavaScript Debug.localhost:54367.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Node.js.web &gt; test.js.executor&quot;: &quot;Run&quot;,
    &quot;Node.js.web &gt; test.ts.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;G:/Study/Python/open-canvas/apps/web/src/components/canvas-editor&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;G:\\Study\\Python\\open-canvas\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.gitee.ui.GiteeSettingsConfigurable&quot;,
    &quot;ts.external.directory.path&quot;: &quot;G:\\Study\\Python\\open-canvas\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\Study\Python\open-canvas\apps\web\src\components\canvas-editor" />
      <recent name="G:\Study\Python\open-canvas\apps\web\src\components\canvas-editor\assets" />
    </key>
  </component>
  <component name="RunManager" selected="Node.js.web &gt; test.js">
    <configuration name="localhost:54367" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:54367">
      <method v="2" />
    </configuration>
    <configuration name="web &gt; test.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/apps/web/src/workers/test.js" typescript-loader="bundled" working-dir="$PROJECT_DIR$/apps/web/src/workers">
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Node.js.web &gt; test.js" />
        <item itemvalue="JavaScript Debug.localhost:54367" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0d69b7b1-a40d-4f31-8a6c-d7ab8735ea65" name="Changes" comment="" />
      <created>1755568199123</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755568199123</updated>
      <workItem from="1755568200291" duration="22526000" />
      <workItem from="1755603524880" duration="703000" />
      <workItem from="1755648905399" duration="23057000" />
      <workItem from="1755680534527" duration="868000" />
    </task>
    <task id="LOCAL-00001" summary="markdown修复前备份">
      <option name="closed" value="true" />
      <created>1755586720622</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755586720622</updated>
    </task>
    <task id="LOCAL-00002" summary="openai模型配置有问题">
      <option name="closed" value="true" />
      <created>1755603579836</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755603579836</updated>
    </task>
    <task id="LOCAL-00003" summary="支持glm模型">
      <option name="closed" value="true" />
      <created>1755679158301</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755679158301</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="markdown修复前备份" />
    <MESSAGE value="openai模型配置有问题" />
    <MESSAGE value="支持glm模型" />
    <option name="LAST_COMMIT_MESSAGE" value="支持glm模型" />
  </component>
</project>